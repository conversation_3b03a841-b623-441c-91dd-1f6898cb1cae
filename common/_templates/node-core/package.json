{"name": "{{ packageName }}", "version": "0.0.1", "description": "{{ description }}", "license": "Apache-2.0", "author": "{{ authorName }}", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/node": "^18", "@vitest/coverage-v8": "~3.0.5", "vitest": "~3.0.5", "sucrase": "^3.32.0"}}