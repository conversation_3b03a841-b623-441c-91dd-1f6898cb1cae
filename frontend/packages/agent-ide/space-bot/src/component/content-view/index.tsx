/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useRef, type PropsWithChildren } from 'react';

import classNames from 'classnames';
import { BotMode } from '@coze-arch/bot-api/developer_api';

import s from './index.module.less';

interface ContentViewProps {
  mode: number;
  className?: string;
  style?: React.CSSProperties;
}
export const ContentView: React.FC<PropsWithChildren<ContentViewProps>> = ({
  mode = 1,
  className,
  style,
  children,
}) => {
  const wrapperRef = useRef<HTMLDivElement>(null);

  const isSingle = mode === BotMode.SingleMode;
  const isMulti = mode === BotMode.MultiMode;
  return (
    <div
      className={classNames(
        'w-full h-full overflow-hidden',
        isSingle && s['wrapper-single'],
        isMulti && s['wrapper-multi'],
        className,
      )}
      style={style}
      ref={wrapperRef}
    >
      {children}
    </div>
  );
};

export default ContentView;
