/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as report_common from './report_common';
import * as flow_platform_audit_common from './flow_platform_audit_common';

export type Int64 = string | number;

export interface GetReportTimesData {
  report_times_datas?: Record<Int64, ReportTimesData>;
}

export interface GetReportTimesRequest {
  object_id_list?: Array<string>;
  object_type?: report_common.ReportObjectType;
}

export interface GetReportTimesResponse {
  code: number;
  message: string;
  data?: GetReportTimesData;
}

export interface ReportData {
  object_id: string;
  object_type: report_common.ReportObjectType;
  reason_codes_str: Array<string>;
  report_uid?: string;
  report_id?: string;
  report_user_name?: string;
  report_time?: string;
  report_object_name?: string;
  report_task_id?: string;
  report_status?: report_common.ReportEventStatus;
  audit_status?: flow_platform_audit_common.AuditStatus;
}

export interface ReportQueryData {
  report_datas?: Array<ReportData>;
  total_count?: number;
}

export interface ReportQueryRequest {
  object_id_list?: Array<string>;
  object_type?: report_common.ReportObjectType;
  report_time_begin?: Int64;
  report_time_end?: Int64;
  report_uid?: Int64;
  page_num?: number;
  page_size?: number;
}

export interface ReportQueryResponse {
  code: number;
  message: string;
  data?: ReportQueryData;
}

export interface ReportTimesData {
  total_report_times?: number;
  current_report_times?: number;
}
/* eslint-enable */
