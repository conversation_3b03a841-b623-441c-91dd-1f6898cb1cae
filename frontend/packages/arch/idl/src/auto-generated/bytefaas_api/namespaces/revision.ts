/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CreateRevisionRequest {
  /** Cluster name */
  cluster: string;
  /** Code revision number (server generates if empty) */
  code_revision_number?: string;
  /** List of code dependencies */
  dependency?: Array<common.Dependency>;
  /** Deploy method. Enums: online-edit, scm, command-line, online-edit, image, lego */
  deploy_method?: string;
  /** Description of the revision */
  description?: string;
  /** Whether to disable build and install, true means disable */
  disable_build_install?: boolean;
  /** Environment variables per VRegion */
  envs?: Record<string, Record<string, string>>;
  /** Formatted environment variables per VRegion */
  format_envs?: Record<string, Array<common.FormatEnvs>>;
  /** Handler function name */
  handler?: string;
  /** Initializer function name */
  initializer?: string;
  /** Whether to enable lazy loading */
  lazyload?: boolean;
  /** Name of the revision */
  name?: string;
  /** Network mode (empty string or bridge) */
  network_mode?: string;
  /** Region name */
  region: string;
  /** Command to run */
  run_cmd?: string;
  /** Runtime name */
  runtime?: string;
  /** Runtime container port */
  runtime_container_port?: number;
  /** Runtime debug container port */
  runtime_debug_container_port?: number;
  /** ID of function to create revision */
  service_id: string;
  /** Source URI of code revision */
  source?: string;
  /** Source type of code revision. Enums: url, tos, scm, ceph, image, image-scm, private-tos, lego */
  source_type?: string;
  /** Whether to enable image lazy loading */
  open_image_lazyload?: boolean;
  /** List of other runtime container ports */
  runtime_other_container_ports?: Array<number>;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
  /** Host unique identifier */
  host_uniq?: common.HostUniq;
  /** Whether the revision is in cell migration */
  in_cell_migration?: boolean;
}

export interface CreateRevisionResponse {
  /** Response code */
  code?: number;
  /** Created revision data */
  data?: common.Revision;
  /** Error message, if any */
  error?: string;
}

export interface DeleteFunctionRevisionRequest {
  /** Cluster name */
  cluster: string;
  /** Region name */
  region: string;
  /** Revision number */
  revision_number: number;
  /** ID of service */
  service_id: string;
}

export interface DeleteFunctionRevisionResponse {
  /** Response code */
  code?: number;
  /** Deleted revision data */
  data?: common.Revision;
  /** Error message, if any */
  error?: string;
}

export interface DownloadRevisionCodeRequest {
  /** Cluster name */
  cluster: string;
  /** Region name */
  region: string;
  /** Revision number */
  revision_number: number;
  /** ID of service */
  service_id: string;
}

export interface DownloadRevisionCodeResponse {
  /** Response code */
  code?: number;
  /** Downloaded code data */
  data?: string;
  /** Error message, if any */
  error?: string;
}

export interface GetClusterRevisionsRequest {
  /** Cluster name */
  cluster: string;
  /** Description filter */
  description?: string;
  /** Whether to format the response */
  format?: boolean;
  /** Limit for pagination */
  limit?: number;
  /** Offset for pagination */
  offset?: number;
  /** Region name */
  region: string;
  /** ID of function to create revision */
  service_id: string;
  /** Whether to include status in response */
  with_status?: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetClusterRevisionsResponse {
  /** Response code */
  code?: number;
  /** List of revision data */
  data?: Array<common.Revision>;
  /** Error message, if any */
  error?: string;
}

export interface GetFunctionRevisionRequest {
  /** Cluster name */
  cluster: string;
  /** Whether to format the response */
  format: boolean;
  /** Region name */
  region: string;
  /** Revision number */
  revision_number: number;
  /** ID of service */
  service_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetFunctionRevisionResponse {
  /** Response code */
  code?: number;
  /** Function revision data */
  data?: common.Revision;
  /** Error message, if any */
  error?: string;
}

export interface GetLatestRevisionRequest {
  /** Cluster name */
  cluster: string;
  /** Whether to format the response */
  format?: boolean;
  /** Region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface GetLatestRevisionResponse {
  /** Response code */
  code?: number;
  /** List of latest revision data */
  data?: Array<LatestRevisionResponseData>;
  /** Error message, if any */
  error?: string;
}

export interface GetOnlineRevisionRequest {
  /** Cluster name */
  cluster: string;
  /** Whether to format the response */
  format?: boolean;
  /** Region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface GetOnlineRevisionResponse {
  /** Response code */
  code?: number;
  /** List of online revision data */
  data?: Array<OnlineRevision>;
  /** Error message, if any */
  error?: string;
}

/** Data for the latest revision */
export interface LatestRevisionResponseData {
  /** Revision ID */
  id?: string;
  /** Revision number */
  number?: number;
  /** Region name */
  region?: string;
  /** Release time */
  released_at?: string;
}

/** Online revision details */
export interface OnlineRevision {
  /** Whether adaptive concurrency mode is enabled */
  adaptive_concurrency_mode?: boolean;
  /** Whether authentication is enabled */
  auth_enable?: boolean;
  /** Base image name */
  base_image?: string;
  /** Build description map */
  build_desc_map?: Record<string, common.BuildDescription>;
  /** Built region package keys */
  built_region_package_keys?: Record<string, string>;
  /** Cluster name */
  cluster?: string;
  /** Code revision ID */
  code_revision_id?: string;
  /** Code revision number */
  code_revision_number?: string;
  /** Whether cold start is disabled, true means disable */
  cold_start_disabled?: boolean;
  /** Cold start time in seconds */
  cold_start_sec?: number;
  /** Whether CORS is enabled */
  cors_enable?: boolean;
  /** Time when the revision was created */
  created_at?: string;
  /** User who created the revision */
  created_by?: string;
  /** Deploy method. Enums: online-edit, scm, command-line, online-edit, image, lego */
  deploy_method?: string;
  /** Description of the revision */
  description?: string;
  /** Whether build and install is disabled, true means disable */
  disable_build_install?: boolean;
  /** Environment variables */
  envs?: Record<string, Record<string, string>>;
  /** Whether exclusive mode is enabled */
  exclusive_mode?: boolean;
  /** Formatted environment variables */
  format_envs?: Record<string, Array<common.FormatEnvs>>;
  /** Function ID */
  function_id?: string;
  /** Whether GDPR is enabled */
  gdpr_enable?: boolean;
  /** Handler function name */
  handler?: string;
  /** Revision ID */
  id?: string;
  /** Images map */
  images?: Record<string, string>;
  /** Initializer function name */
  initializer?: string;
  /** Initializer time in seconds */
  initializer_sec?: number;
  /** Disabled zones map */
  is_this_zone_disabled?: Record<string, boolean>;
  /** Last ticket status */
  last_ticket_status?: string;
  /** Latency in seconds */
  latency_sec?: number;
  /** Whether lazy loading is enabled */
  lazyload?: boolean;
  /** Maximum concurrency */
  max_concurrency?: number;
  /** Name of the revision */
  name?: string;
  /** Revision number */
  number?: number;
  /** PSM name */
  psm?: string;
  /** Resource limit configuration */
  resource_limit?: common.ResourceLimit;
  /** Command to run */
  run_cmd?: string;
  /** Runtime name */
  runtime?: string;
  /** Service ID */
  service_id?: string;
  /** Source code */
  source?: string;
  /** Source download URL */
  source_download_url?: string;
  /** Source type */
  source_type?: string;
  /** Throttle log bytes per second */
  throttle_log_bytes_per_sec?: number;
  /** Throttle stderr log bytes per second */
  throttle_stderr_log_bytes_per_sec?: number;
  /** Throttle stdout log bytes per second */
  throttle_stdout_log_bytes_per_sec?: number;
  /** Traffic value */
  traffic_value?: number;
  /** Time when the revision was last updated */
  updated_at?: string;
  /** Worker built region package keys */
  worker_built_region_package_keys?: Record<string, common.EmptyObject>;
  /** Whether online mode is enabled */
  online_mode?: boolean;
  /** Whether image lazy loading is enabled */
  open_image_lazyload?: boolean;
  /** Whether overload protection is enabled */
  overload_protect_enabled?: boolean;
  /** Whether Consul IPv6 register is enabled */
  enable_consul_ipv6_register?: boolean;
  /** Whether system mount is enabled */
  enable_sys_mount?: boolean;
  /** Whether mounting JWT bundles is disabled, true means disable */
  disable_mount_jwt_bundles?: boolean;
  /** Termination grace period in seconds */
  termination_grace_period_seconds?: number;
  /** Whether Consul register is enabled */
  enable_consul_register?: boolean;
  /** enable privileged in pod */
  privileged?: boolean;
}

export interface UpdateFunctionLatestRevisionRequest {
  /** Cluster name */
  cluster: string;
  /** Region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface UpdateFunctionLatestRevisionResponse {
  /** Response code */
  code?: number;
  /** Data for the update operation */
  data?: string;
  /** Error message, if any */
  error?: string;
}

export interface UpdateFunctionRevisionRequest {
  /** Cluster name */
  cluster: string;
  /** Deploy method. Enums: online-edit, scm, command-line, online-edit, image, lego */
  deploy_method?: string;
  /** Environment variables */
  envs?: Record<string, Record<string, string>>;
  /** Handler function name */
  handler?: string;
  /** Region name */
  region: string;
  /** Revision number */
  revision_number: number;
  /** Runtime name */
  runtime?: string;
  /** ID of service */
  service_id: string;
  /** Source code */
  source?: string;
  /** Type of code source */
  source_type?: string;
}

export interface UpdateFunctionRevisionResponse {
  /** Response code */
  code?: number;
  /** Updated revision data */
  data?: common.Revision;
  /** Error message, if any */
  error?: string;
}
/* eslint-enable */
