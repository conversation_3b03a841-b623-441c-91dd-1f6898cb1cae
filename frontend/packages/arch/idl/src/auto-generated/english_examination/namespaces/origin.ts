/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export enum GradeExamClientType {
  /** 小程序 */
  MiniProgram = 0,
  /** 网页端 */
  Web = 1,
}

/** 原味考试
 ======================= 枚举 ======================= */
export enum GradeExamStatus {
  /** 进行中 */
  InProgress = 0,
  /** 已完成 */
  Finished = 1,
  /** 待批改 */
  ToBeChecked = 3,
}

export enum GradeExamSyncType {
  /** 不同步 */
  NotSync = 0,
  /** 同步 */
  Sync = 1,
}

export enum GradeExamType {
  /** 定级 */
  Rank = 0,
  /** 单元 */
  Unit = 1,
  /** 新定级人工 */
  NewRankManual = 2,
  /** 新定级机改 */
  NewRankMachine = 3,
}

export enum LibraryQuestionPart {
  /** Part 1 */
  One = 1,
  /** Part 2 */
  Two = 2,
  /** Part 3 */
  Three = 3,
  /** Part 4 */
  Four = 4,
}

export enum LibraryQuestionStatus {
  /** 下线 */
  Offline = 0,
  /** 上线 */
  Online = 1,
}

export enum LibraryQuestionType {
  /** 词汇 */
  Word = 0,
  /** 语法 */
  Grammar = 1,
  /** 听力 */
  Listening = 2,
  /** 口语 */
  Speaking = 3,
  /** 新口语题 */
  NewSpeaking = 4,
}

export interface CreateLibraryQuestion {
  content?: string;
  options?: Array<string>;
  answer?: number;
  type?: LibraryQuestionType;
  level?: string;
  /** 音频 */
  audio_uri?: string;
  /** 新口语题 - 部分 */
  part?: LibraryQuestionPart;
  /** 新口语题 - 图片 */
  image_uri?: string;
}

export interface GradeExam {
  id?: Int64;
  /** 用户ID */
  user_id?: Int64;
  /** 团队考试ID */
  team_exam_id?: Int64;
  /** 等级 */
  level?: string;
  /** 年级 */
  grade?: string;
  /** 开始时间 */
  start_duration?: number;
  /** 客户端类型 */
  client_type?: GradeExamClientType;
  /** 状态 */
  status?: GradeExamStatus;
  /** 类型 */
  type?: GradeExamType;
  /** 同步apaas */
  sync_type?: GradeExamSyncType;
  /** 题目列表 */
  questions?: Array<GradeExamLibraryQuestion>;
  /** 创建时间 */
  created_at?: Int64;
}

export interface GradeExamLibraryQuestion {
  id?: Int64;
  /** 考试ID */
  grade_exam_id?: Int64;
  /** 题库题目ID */
  library_question_id?: Int64;
  /** 轮次ID */
  round_id?: Int64;
  /** 用户答案 */
  user_answer?: string;
  /** 题库题目 */
  library_question?: LibraryQuestion;
}

export interface LibraryQuestion {
  id?: Int64;
  /** 题干 */
  content?: string;
  /** 选项 */
  options?: string;
  /** 旧tos音频 */
  audio?: string;
  /** imageX音频 */
  audio_uri?: string;
  /** 答案 */
  answer?: number;
  /** 等级 */
  level?: string;
  /** 类型 */
  type?: LibraryQuestionType;
  /** 状态 */
  status?: LibraryQuestionStatus;
  /** 新口语题 - 部分 */
  part?: LibraryQuestionPart;
  /** 新口语题 - 题干音频 */
  content_audio_uri?: string;
  /** 新口语题 - 图片 */
  image_uri?: string;
  /** imageX音频Url */
  audio_url?: string;
  /** 新口语题 - 题干音频Url */
  content_audio_url?: string;
  /** 新口语题 - 图片Url */
  image_url?: string;
}

/** ======================= 响应模型 ======================= */
export interface Team {
  id?: Int64;
  /** 团队名称 */
  name?: string;
  /** 团队头像 */
  avatar?: string;
}

export interface TeamExam {
  id?: Int64;
  /** 团队ID */
  team_id?: Int64;
  /** 批改类型 */
  check_type?: common.TeamExamCheckType;
  /** 状态 */
  status?: common.TeamExamStatus;
  /** 团队 */
  team?: Team;
  /** 用户考试记录 */
  user_team_exam?: UserTeamExam;
}

export interface UpdateLibraryQuestion {
  /** 题目ID */
  id?: Int64;
  /** 题干 */
  content?: string;
  /** 选项 */
  options?: Array<string>;
  /** 答案 */
  answer?: number;
  /** 状态 */
  status?: LibraryQuestionStatus;
  /** tos音频 */
  audio?: string;
  /** 音频 */
  audio_uri?: string;
  /** 新口语题 - 图片 */
  image_uri?: string;
}

export interface UserTeamExam {
  id?: Int64;
  /** 用户ID */
  user_id?: Int64;
  /** 团队考试ID */
  team_exam_id?: Int64;
  /** 等级考试ID */
  grade_exam_id?: Int64;
}
/* eslint-enable */
