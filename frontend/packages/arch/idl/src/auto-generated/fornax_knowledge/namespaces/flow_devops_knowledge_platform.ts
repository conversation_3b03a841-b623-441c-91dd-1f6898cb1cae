/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_knowledge_common from './flow_devops_knowledge_common';
import * as base from './base';
import * as flow_devops_knowledge_retrive from './flow_devops_knowledge_retrive';

export type Int64 = string | number;

export interface BatchAddKnowledgeDocumentReq {
  knowledge_documents?: Array<flow_devops_knowledge_common.KnowledgeDocument>;
  files?: Array<flow_devops_knowledge_common.KnowledgeDocumentFile>;
  creator_id?: Int64;
  updator_id?: Int64;
  base?: base.Base;
}

export interface BatchAddKnowledgeDocumentResp {
  success_list?: Array<flow_devops_knowledge_common.KnowledgeDocument>;
  failure_list?: Array<flow_devops_knowledge_common.KnowledgeDocument>;
  /** 比如返回“不支持添加L3、L4密级的文档” */
  failure_reasons?: Array<string>;
  base_resp?: base.BaseResp;
}

export interface BatchDeleteKnowledgeDocumentReq {
  knowledge_document_ids?: Array<Int64>;
  knowledge_id?: Int64;
  operator_id?: Int64;
  base?: base.Base;
}

export interface BatchDeleteKnowledgeDocumentResp {
  base_resp?: base.BaseResp;
}

export interface CheckAuthStatusReq {
  channel?: flow_devops_knowledge_common.Channel;
  resource_type?: flow_devops_knowledge_common.ResourceType;
  user_id?: Int64;
  base?: base.Base;
}

export interface CheckAuthStatusResp {
  is_valid?: boolean;
  base_resp?: base.BaseResp;
}

export interface CreateKnowledgeShelfDocumentReq {
  knowledge_shelf_document?: flow_devops_knowledge_common.KnowledgeShelfDocument;
  base?: base.Base;
}

export interface CreateKnowledgeShelfDocumentResp {
  knowledge_shelf_document?: flow_devops_knowledge_common.KnowledgeShelfDocument;
  base_resp?: base.BaseResp;
}

export interface DeleteKnowledgeReq {
  space_id?: Int64;
  knowledge_id?: Int64;
  operator_id?: Int64;
  base?: base.Base;
}

export interface DeleteKnowledgeResp {
  base_resp?: base.BaseResp;
}

export interface DeleteKnowledgeShelfDocumentReq {
  knowledge_shelf_document_id?: Int64;
  space_id?: Int64;
  knowledge_id?: Int64;
  operator_id?: Int64;
  base?: base.Base;
}

export interface DeleteKnowledgeShelfDocumentResp {
  base_resp?: base.BaseResp;
}

export interface EmbeddingCallbackReq {
  texts?: Array<string>;
  model_id?: Int64;
  base?: base.Base;
}

export interface EmbeddingCallbackResp {
  vectors?: Array<Array<number>>;
  error?: string;
  base_resp?: base.BaseResp;
}

export interface EventCallbackReq {
  event?: flow_devops_knowledge_common.CallbackEvent;
  base?: base.Base;
}

export interface EventCallbackResp {
  base_resp?: base.BaseResp;
}

export interface GenerateIDReq {
  number?: number;
  base?: base.Base;
}

export interface GenerateIDResp {
  id_list?: Array<Int64>;
  base_resp?: base.BaseResp;
}

export interface GetFeishuFileMetaReq {
  resource_type?: flow_devops_knowledge_common.ResourceType;
  file?: flow_devops_knowledge_common.KnowledgeDocumentFile;
  operator_id?: Int64;
  base?: base.Base;
}

export interface GetFeishuFileMetaResp {
  resource_type?: flow_devops_knowledge_common.ResourceType;
  title?: string;
  owner_id?: string;
  owner_user_info?: flow_devops_knowledge_common.UserInfo;
  sec_label_name?: string;
  base_resp?: base.BaseResp;
}

export interface GetFeishuWikiNodeReq {
  url?: string;
  space_id?: string;
  base?: base.Base;
}

export interface GetFeishuWikiNodeResp {
  node?: flow_devops_knowledge_common.WikiNode;
  base_resp?: base.BaseResp;
}

export interface GetKnowledgeByIDReq {
  space_id?: Int64;
  knowledge_id?: Int64;
  base?: base.Base;
}

export interface GetKnowledgeByIDResp {
  knowledge?: flow_devops_knowledge_common.Knowledge;
  base_resp?: base.BaseResp;
}

export interface GetKnowledgeChunkListReq {
  knowledge_document?: flow_devops_knowledge_common.KnowledgeDocument;
  last_seq_id?: number;
  limit?: number;
  base?: base.Base;
}

export interface GetKnowledgeChunkListResp {
  chunk_info_list?: Array<flow_devops_knowledge_common.ChunkInfo>;
  last_seq_id?: number;
  total?: number;
  base_resp?: base.BaseResp;
}

export interface GetPreviewChunkListReq {
  space_id?: Int64;
  knowledge_document_file?: flow_devops_knowledge_common.KnowledgeDocumentFile;
  chunk_rule?: flow_devops_knowledge_common.ChunkRule;
  base?: base.Base;
}

export interface GetPreviewChunkListResp {
  chunk_info_list?: Array<flow_devops_knowledge_common.ChunkInfo>;
  total?: number;
  base_resp?: base.BaseResp;
}

export interface MGetKnowledgeDocumentByParentReq {
  space_id?: Int64;
  parent_knowledge_doc_id?: Int64;
  knowledge_id?: Int64;
  offset?: number;
  limit?: number;
  base?: base.Base;
}

export interface MGetKnowledgeDocumentByParentResp {
  knowledge_documents?: Array<flow_devops_knowledge_common.KnowledgeDocument>;
  total?: number;
  base_resp?: base.BaseResp;
}

export interface MGetKnowledgeDocumentEntryReq {
  space_id?: Int64;
  knowledge_id?: Int64;
  knowledge_shelf_document_id?: Int64;
  /** 数据来源 */
  resource_type?: flow_devops_knowledge_common.ResourceType;
  offset?: number;
  limit?: number;
  base?: base.Base;
}

export interface MGetKnowledgeDocumentEntryResp {
  knowledge_document_entries?: Array<flow_devops_knowledge_common.KnowledgeDocumentEntry>;
  knowledge_document_map?: Record<
    Int64,
    flow_devops_knowledge_common.KnowledgeDocument
  >;
  total?: number;
  base_resp?: base.BaseResp;
}

export interface MGetKnowledgeDocumentReq {
  space_id?: Int64;
  /** 用于搜索 */
  search_content?: string;
  knowledge_id?: Int64;
  knowledge_shelf_document_id?: Int64;
  /** 数据来源 */
  resource_types?: Array<flow_devops_knowledge_common.ResourceType>;
  offset?: number;
  limit?: number;
  base?: base.Base;
}

export interface MGetKnowledgeDocumentResp {
  knowledge_documents?: Array<flow_devops_knowledge_common.KnowledgeDocument>;
  total?: number;
  base_resp?: base.BaseResp;
}

export interface MGetKnowledgeReq {
  space_id?: Int64;
  /** 用于搜索 */
  search_content?: string;
  offset?: number;
  limit?: number;
  base?: base.Base;
}

export interface MGetKnowledgeResp {
  knowledges?: Array<flow_devops_knowledge_common.Knowledge>;
  total?: number;
  base_resp?: base.BaseResp;
}

export interface MGetKnowledgeShelfDocumentReq {
  space_id?: Int64;
  knowledge_id?: Int64;
  knowledge_shelf_document_id?: Int64;
  type?: flow_devops_knowledge_common.KnowledgeShelfDocumentType;
  /** 用于搜索 */
  search_content?: string;
  offset?: number;
  limit?: number;
  base?: base.Base;
}

export interface MGetKnowledgeShelfDocumentResp {
  knowledge_shelf_documents?: Array<flow_devops_knowledge_common.KnowledgeShelfDocument>;
  total?: number;
  base_resp?: base.BaseResp;
}

export interface RetrieveTestReq {
  knowledge_keys?: Array<string>;
  query?: string;
  channels?: Array<flow_devops_knowledge_retrive.Channel>;
  top_k?: number;
  rank?: flow_devops_knowledge_retrive.Ranker;
  space_id?: Int64;
  base?: base.Base;
}

export interface RetrieveTestResp {
  code?: number;
  msg?: string;
  data?: flow_devops_knowledge_retrive.RecallData;
  retrieve_cost?: number;
  base_resp?: base.BaseResp;
}

export interface StartEmbeddingReq {
  knowledge_document_id?: Int64;
  chunk_rule?: flow_devops_knowledge_common.ChunkRule;
  base?: base.Base;
}

export interface StartEmbeddingResp {
  base_resp?: base.BaseResp;
}

export interface UpsertKnowledgeReq {
  knowledge?: flow_devops_knowledge_common.Knowledge;
  base?: base.Base;
}

export interface UpsertKnowledgeResp {
  knowledge?: flow_devops_knowledge_common.Knowledge;
  base_resp?: base.BaseResp;
}
/* eslint-enable */
