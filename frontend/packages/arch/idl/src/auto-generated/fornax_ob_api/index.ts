/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as annotation from './namespaces/annotation';
import * as authz from './namespaces/authz';
import * as base from './namespaces/base';
import * as datasetv2 from './namespaces/datasetv2';
import * as datasetv2similarity from './namespaces/datasetv2similarity';
import * as eval_set from './namespaces/eval_set';
import * as eval_target from './namespaces/eval_target';
import * as evaluation_domain_common from './namespaces/evaluation_domain_common';
import * as evaluator from './namespaces/evaluator';
import * as filter from './namespaces/filter';
import * as flow_devops_fornaxob_common from './namespaces/flow_devops_fornaxob_common';
import * as flow_devops_fornaxob_fieldfilter from './namespaces/flow_devops_fornaxob_fieldfilter';
import * as flow_devops_fornaxob_fieldfilterv2 from './namespaces/flow_devops_fornaxob_fieldfilterv2';
import * as flow_devops_fornaxob_indicatorservice from './namespaces/flow_devops_fornaxob_indicatorservice';
import * as flow_devops_fornaxob_metricsservice from './namespaces/flow_devops_fornaxob_metricsservice';
import * as flow_devops_fornaxob_operationservice from './namespaces/flow_devops_fornaxob_operationservice';
import * as flow_devops_fornaxob_taskservice from './namespaces/flow_devops_fornaxob_taskservice';
import * as flow_devops_fornaxob_threadservice from './namespaces/flow_devops_fornaxob_threadservice';
import * as flow_devops_fornaxob_traceservice from './namespaces/flow_devops_fornaxob_traceservice';
import * as flow_devops_fornaxob_viewservice from './namespaces/flow_devops_fornaxob_viewservice';
import * as fornaxob_domain_common from './namespaces/fornaxob_domain_common';
import * as metrics from './namespaces/metrics';
import * as operation from './namespaces/operation';
import * as query from './namespaces/query';
import * as span from './namespaces/span';
import * as stone_fornax_evaluation_expt from './namespaces/stone_fornax_evaluation_expt';
import * as tag from './namespaces/tag';
import * as task from './namespaces/task';

export {
  annotation,
  authz,
  base,
  datasetv2,
  datasetv2similarity,
  eval_set,
  eval_target,
  evaluation_domain_common,
  evaluator,
  filter,
  flow_devops_fornaxob_common,
  flow_devops_fornaxob_fieldfilter,
  flow_devops_fornaxob_fieldfilterv2,
  flow_devops_fornaxob_indicatorservice,
  flow_devops_fornaxob_metricsservice,
  flow_devops_fornaxob_operationservice,
  flow_devops_fornaxob_taskservice,
  flow_devops_fornaxob_threadservice,
  flow_devops_fornaxob_traceservice,
  flow_devops_fornaxob_viewservice,
  fornaxob_domain_common,
  metrics,
  operation,
  query,
  span,
  stone_fornax_evaluation_expt,
  tag,
  task,
};
export * from './namespaces/annotation';
export * from './namespaces/authz';
export * from './namespaces/base';
export * from './namespaces/datasetv2';
export * from './namespaces/datasetv2similarity';
export * from './namespaces/eval_set';
export * from './namespaces/eval_target';
export * from './namespaces/evaluation_domain_common';
export * from './namespaces/evaluator';
export * from './namespaces/filter';
export * from './namespaces/flow_devops_fornaxob_common';
export * from './namespaces/flow_devops_fornaxob_fieldfilter';
export * from './namespaces/flow_devops_fornaxob_fieldfilterv2';
export * from './namespaces/flow_devops_fornaxob_indicatorservice';
export * from './namespaces/flow_devops_fornaxob_metricsservice';
export * from './namespaces/flow_devops_fornaxob_operationservice';
export * from './namespaces/flow_devops_fornaxob_taskservice';
export * from './namespaces/flow_devops_fornaxob_threadservice';
export * from './namespaces/flow_devops_fornaxob_traceservice';
export * from './namespaces/flow_devops_fornaxob_viewservice';
export * from './namespaces/fornaxob_domain_common';
export * from './namespaces/metrics';
export * from './namespaces/operation';
export * from './namespaces/query';
export * from './namespaces/span';
export * from './namespaces/stone_fornax_evaluation_expt';
export * from './namespaces/tag';
export * from './namespaces/task';

export type Int64 = string | number;

export default class FornaxObApiService<T> {
  private request: any = () => {
    throw new Error('FornaxObApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/observation/v1/traces
   *
   * 查询通用链路列表
   */
  ListTraces(
    req: flow_devops_fornaxob_traceservice.ListTracesRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.ListTracesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/traces');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      filters: _req['filters'],
      full_text_search: _req['full_text_search'],
      limit: _req['limit'],
      order_by: _req['order_by'],
      page_token: _req['page_token'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
      platform_type: _req['platform_type'],
      query_and_or: _req['query_and_or'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/observation/v1/trace/:trace_id
   *
   * 通过trace ID查询链路详情
   */
  GetTrace(
    req: flow_devops_fornaxob_traceservice.GetTraceRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.GetTraceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/observation/v1/trace/${_req['trace_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
      platform_type: _req['platform_type'],
      log_id: _req['log_id'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/observation/v1/traces/meta_info
   *
   * 查询元信息，包括字段类型，filter opertor
   */
  GetTracesMetaInfo(
    req?: flow_devops_fornaxob_traceservice.GetTracesMetaInfoRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.GetTracesMetaInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/observation/v1/traces/meta_info');
    const method = 'GET';
    const params = {
      platform_type: _req['platform_type'],
      span_list_type: _req['span_list_type'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/observation/v1/metrics/tags/options
   *
   * 获取运维指标过滤项值的选项
   */
  GetTagsOptions(
    req: flow_devops_fornaxob_metricsservice.GetTagsOptionsRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_metricsservice.GetTagsOptionsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/metrics/tags/options');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
      client_names: _req['client_names'],
      client_name: _req['client_name'],
      app_id: _req['app_id'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/observation/v1/metrics
   *
   * 获取  metrics 详情
   */
  GetMetrics(
    req: flow_devops_fornaxob_metricsservice.GetMetricsRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_metricsservice.GetMetricsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/metrics');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      metrics_type: _req['metrics_type'],
      aggregate_type: _req['aggregate_type'],
      tag_kvs: _req['tag_kvs'],
      filters: _req['filters'],
      top_k: _req['top_k'],
      client_names: _req['client_names'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/observation/v1/trace_by_logid/:log_id
   *
   * 通过log ID查询链路详情
   */
  GetTraceByLogID(
    req: flow_devops_fornaxob_traceservice.GetTraceByLogIDRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.GetTraceByLogIDResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/observation/v1/trace_by_logid/${_req['log_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/observation/v1/traces/export_to_evaluation
   *
   * Trace导入到评测数据集
   */
  ExportTracesToEvaluation(
    req: flow_devops_fornaxob_traceservice.ExportTracesToEvaluationRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.ExportTracesToEvaluationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/observation/v1/traces/export_to_evaluation',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      spans: _req['spans'],
      datasets: _req['datasets'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      platform_type: _req['platform_type'],
      span_list_type: _req['span_list_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/operation
   *
   * 查询运营信息
   */
  QueryOperation(
    req: flow_devops_fornaxob_operationservice.QueryOperationRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_operationservice.QueryOperationResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/operation');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      operation_type: _req['operation_type'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      psm: _req['psm'],
      aggregation_type: _req['aggregation_type'],
      model_id: _req['model_id'],
      fornax_env: _req['fornax_env'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/operation/common_aggregation
   *
   * 查询运营指标聚合信息，比如psm/model等
   */
  GetCommonOperationAggregationInfo(
    req: flow_devops_fornaxob_operationservice.GetCommonOperationAggregationRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_operationservice.GetCommonOperationAggregationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/observation/v1/operation/common_aggregation',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      aggregation_keys: _req['aggregation_keys'],
      fornax_env: _req['fornax_env'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/traces/batch_get_advance_info
   *
   * 批量查询链路进阶信息
   */
  BatchGetTracesAdvanceInfo(
    req: flow_devops_fornaxob_traceservice.BatchGetTracesAdvanceInfoRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.BatchGetTracesAdvanceInfoResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/observation/v1/traces/batch_get_advance_info',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      traces: _req['traces'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
      platform_type: _req['platform_type'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/observation/v1/threads
   *
   * 查询会话列表
   */
  ListThreads(
    req: flow_devops_fornaxob_threadservice.ListThreadsRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_threadservice.ListThreadsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/threads');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      filters: _req['filters'],
      full_text_search: _req['full_text_search'],
      limit: _req['limit'],
      page_token: _req['page_token'],
      platform_type: _req['platform_type'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/observation/v1/internal/query_offline
   *
   * boe通用转发接口
   */
  QueryOffline(
    req: flow_devops_fornaxob_common.QueryOfflineRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_common.QueryOfflineResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/internal/query_offline');
    const method = 'POST';
    const data = { type: _req['type'], body: _req['body'] };
    const headers = { 'x-zti-token': _req['x-zti-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/observation/v1/thread
   *
   * thread ID查询会话详情
   */
  GetThread(
    req: flow_devops_fornaxob_threadservice.GetThreadRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_threadservice.GetThreadResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/thread');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      thread_id: _req['thread_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      limit: _req['limit'],
      order_type: _req['order_type'],
      page_token: _req['page_token'],
      platform_type: _req['platform_type'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/observation/v1/threads/config
   *
   * 查询会话元信息，包括字段类型，filter opertor
   */
  GetThreadsConfig(
    req: flow_devops_fornaxob_threadservice.GetThreadsConfigRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_threadservice.GetThreadsConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/threads/config');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      platform_type: _req['platform_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/observation/v1/traces/all_spans
   *
   * 查询 All Span List
   */
  ListAllSpans(
    req: flow_devops_fornaxob_traceservice.ListAllSpansRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.ListAllSpansResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/traces/all_spans');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      filters: _req['filters'],
      full_text_search: _req['full_text_search'],
      limit: _req['limit'],
      order_by: _req['order_by'],
      page_token: _req['page_token'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
      platform_type: _req['platform_type'],
      is_root_span_only: _req['is_root_span_only'],
      query_and_or: _req['query_and_or'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/observation/v1/traces/llm_spans
   *
   * 查询 LLM Span List
   */
  ListLLMSpans(
    req: flow_devops_fornaxob_traceservice.ListLLMSpansRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.ListLLMSpansResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/traces/llm_spans');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      filters: _req['filters'],
      full_text_search: _req['full_text_search'],
      limit: _req['limit'],
      order_by: _req['order_by'],
      page_token: _req['page_token'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
      platform_type: _req['platform_type'],
      query_and_or: _req['query_and_or'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/observation/v1/thread/thread_meta
   *
   * query ID查询会话user元信息
   */
  GetThreadMeta(
    req: flow_devops_fornaxob_threadservice.GetThreadMetaRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_threadservice.GetThreadMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/thread/thread_meta');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      thread_id: _req['thread_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      platform_type: _req['platform_type'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/observation/v1/insight_indicator
   *
   * 查询洞察指标信息
   */
  QueryInsightIndicators(
    req: flow_devops_fornaxob_indicatorservice.QueryInsightIndicatorsRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_indicatorservice.QueryInsightIndicatorsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/insight_indicator');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      indicator_type: _req['indicator_type'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      filter: _req['filter'],
      aggregation_type: _req['aggregation_type'],
      overview_indicator_type: _req['overview_indicator_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/trace/search_v3
   *
   * 通过log ID/trace ID条件查询链路详情
   */
  SearchTraceV3(
    req: flow_devops_fornaxob_traceservice.SearchTraceV3Request,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.SearchTraceV3Response> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/trace/search_v3');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      id: _req['id'],
      search_type: _req['search_type'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
      scan_span_in_hour: _req['scan_span_in_hour'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/observation/v1/indicator_options
   *
   * 查询洞察指标Option信息
   */
  GetIndicatorOptions(
    req: flow_devops_fornaxob_indicatorservice.GetIndicatorOptionsRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_indicatorservice.GetIndicatorOptionsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/indicator_options');
    const method = 'POST';
    const data = { options: _req['options'], space_id: _req['space_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/views/list_view
   *
   * 读取视图列表
   */
  ListViews(
    req: flow_devops_fornaxob_viewservice.ListViewsRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_viewservice.ListViewsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/views/list_view');
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      fornax_space_id: _req['fornax_space_id'],
      view_name: _req['view_name'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/views/create_view
   *
   * 创建一个视图
   */
  CreateView(
    req: flow_devops_fornaxob_viewservice.CreateViewRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_viewservice.CreateViewResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/views/create_view');
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      fornax_space_id: _req['fornax_space_id'],
      view_name: _req['view_name'],
      platform_type: _req['platform_type'],
      filters: _req['filters'],
      base: _req['base'],
    };
    const params = { span_list_type: _req['span_list_type'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/observation/v1/views/update_view
   *
   * 更新一个视图
   */
  UpdateView(
    req: flow_devops_fornaxob_viewservice.UpdateViewRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_viewservice.UpdateViewResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/views/update_view');
    const method = 'POST';
    const data = {
      id: _req['id'],
      fornax_space_id: _req['fornax_space_id'],
      view_name: _req['view_name'],
      platform_type: _req['platform_type'],
      filters: _req['filters'],
      base: _req['base'],
    };
    const params = { span_list_type: _req['span_list_type'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/observation/v1/views/delete_view
   *
   * 删除一个视图
   */
  DeleteView(
    req: flow_devops_fornaxob_viewservice.DeleteViewRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_viewservice.DeleteViewResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/views/delete_view');
    const method = 'POST';
    const data = {
      id: _req['id'],
      fornax_space_id: _req['fornax_space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /v1/loop/traces/ingest
   *
   * trace上报
   */
  IngestTraces(
    req?: flow_devops_fornaxob_traceservice.IngestTracesRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.IngestTracesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/loop/traces/ingest');
    const method = 'POST';
    const data = { spans: _req['spans'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/indicator_by_option
   *
   * 查询不同Option的指标
   */
  QueryInsightIndicatorByOption(
    req: flow_devops_fornaxob_indicatorservice.QueryInsightIndicatorByOptionRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_indicatorservice.QueryInsightIndicatorByOptionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/indicator_by_option');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      indicator_type: _req['indicator_type'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      options: _req['options'],
      aggregation_type: _req['aggregation_type'],
      app_type: _req['app_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/trace/get_span_info
   *
   * 通过span ID查询span详情
   */
  GetSpanInfo(
    req: flow_devops_fornaxob_traceservice.GetSpanInfoRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.GetSpanInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/trace/get_span_info');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      trace_id: _req['trace_id'],
      span_id: _req['span_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      target_env: _req['target_env'],
      transferred: _req['transferred'],
    };
    const headers = { 'x-boe-env': _req['x-boe-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/observation/v1/traces/export_to_evaluation_set
   *
   * Trace导入到评测数据集
   */
  ExportTracesToEvaluationSet(
    req: flow_devops_fornaxob_traceservice.ExportTracesToEvaluationSetRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.ExportTracesToEvaluationSetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/observation/v1/traces/export_to_evaluation_set',
    );
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      span_ids: _req['span_ids'],
      evaluation_set: _req['evaluation_set'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      platform_type: _req['platform_type'],
      export_type: _req['export_type'],
      field_mappings: _req['field_mappings'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/observation/v1/tasks/:task_id
   *
   * 更新任务
   */
  UpdateTask(
    req: flow_devops_fornaxob_taskservice.UpdateTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_taskservice.UpdateTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/observation/v1/tasks/${_req['task_id']}`);
    const method = 'PUT';
    const data = {
      workspace_id: _req['workspace_id'],
      task_status: _req['task_status'],
      description: _req['description'],
      effective_time: _req['effective_time'],
      sample_rate: _req['sample_rate'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/tasks/list
   *
   * 任务列表
   */
  ListTasks(
    req: flow_devops_fornaxob_taskservice.ListTasksRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_taskservice.ListTasksResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/tasks/list');
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      task_filters: _req['task_filters'],
      limit: _req['limit'],
      offset: _req['offset'],
      order_by: _req['order_by'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/traces/change_eval_score
   *
   * 修改评估结果
   */
  ChangeEvaluatorScore(
    req: flow_devops_fornaxob_traceservice.ChangeEvaluatorScoreRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.ChangeEvaluatorScoreResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/traces/change_eval_score');
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      evaluator_record_id: _req['evaluator_record_id'],
      span_id: _req['span_id'],
      start_time: _req['start_time'],
      correction: _req['correction'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/observation/v1/tasks/:task_id
   *
   * 任务详情
   */
  GetTask(
    req: flow_devops_fornaxob_taskservice.GetTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_taskservice.GetTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/observation/v1/tasks/${_req['task_id']}`);
    const method = 'GET';
    const params = { workspace_id: _req['workspace_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/observation/v1/annotation/list_evaluators
   *
   * 获取标签筛选的评估器列表
   */
  ListAnnotationEvaluators(
    req: flow_devops_fornaxob_traceservice.ListAnnotationEvaluatorsRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.ListAnnotationEvaluatorsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/observation/v1/annotation/list_evaluators',
    );
    const method = 'GET';
    const params = { workspace_id: _req['workspace_id'], name: _req['name'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/observation/v1/tasks
   *
   * 创建任务
   */
  CreateTask(
    req: flow_devops_fornaxob_taskservice.CreateTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_taskservice.CreateTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/tasks');
    const method = 'POST';
    const data = { task: _req['task'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/traces/preview_export_to_evaluation_set
   *
   * Trace导入到评测数据集试运行
   */
  PreviewExportTracesToEvaluation(
    req: flow_devops_fornaxob_traceservice.PreviewExportTracesToEvaluationRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.PreviewExportTracesToEvaluationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/observation/v1/traces/preview_export_to_evaluation_set',
    );
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      span_ids: _req['span_ids'],
      evaluation_set: _req['evaluation_set'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      platform_type: _req['platform_type'],
      export_type: _req['export_type'],
      field_mappings: _req['field_mappings'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observation/v1/tasks/check_name
   *
   * 校验task名称
   */
  CheckTaskName(
    req: flow_devops_fornaxob_taskservice.CheckTaskNameRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_taskservice.CheckTaskNameResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observation/v1/tasks/check_name');
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      name: _req['name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /v1/loop/opentelemetry/v1/traces
   *
   * otel trace上报
   */
  OtelIngestTraces(
    req: flow_devops_fornaxob_traceservice.OtelIngestTracesRequest,
    options?: T,
  ): Promise<flow_devops_fornaxob_traceservice.OtelIngestTracesResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/loop/opentelemetry/v1/traces');
    const method = 'POST';
    const data = { body: _req['body'], Base: _req['Base'] };
    const headers = {
      ContentType: _req['ContentType'],
      ContentEncoding: _req['ContentEncoding'],
      SpaceID: _req['SpaceID'],
    };
    return this.request({ url, method, data, headers }, options);
  }
}
/* eslint-enable */
