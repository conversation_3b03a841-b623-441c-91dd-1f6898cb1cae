{"name": "@coze-common/chat-answer-action", "version": "0.0.1", "description": "Chat Answer Action", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts", "./hooks/*": "./src/hooks/*"}, "main": "src/index.ts", "typesVersions": {"*": {"hooks/*": ["./src/hooks/*"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-flags": "workspace:*", "@coze-common/chat-area-plugin-message-grab": "workspace:*", "@coze-common/chat-uikit": "workspace:*", "@coze-common/chat-uikit-shared": "workspace:*", "@coze-common/text-grab": "workspace:*", "classnames": "^2.3.2", "immer": "^10.0.3"}, "devDependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-common/chat-area": "workspace:*", "@coze-common/chat-area-utils": "workspace:*", "@coze-common/chat-core": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "ahooks": "^3.7.8", "copy-to-clipboard": "^3.3.3", "lodash-es": "^4.17.21", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5", "zustand": "^4.4.7"}, "peerDependencies": {"@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-common/chat-area": "workspace:*", "@coze-common/chat-area-utils": "workspace:*", "@coze-common/chat-core": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "ahooks": "^3.7.8", "copy-to-clipboard": "^3.3.3", "react": ">=18.2.0", "react-dom": ">=18.2.0", "zustand": "^4.4.7"}}