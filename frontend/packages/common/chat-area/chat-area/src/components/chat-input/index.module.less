.chat-input {
  position: relative;

  display: flex;
  flex-direction: column;
  align-items: center;

  width: 100%;
}

// 看起来是没人用的，来个勇者删掉他
.chat-input-wrapper {
  width: 100%;
  padding: 0 24px;
}

.safe-area {
  width: 100%;
}

.safe-area-pc {
  height: 24px;
}

.safe-area-mobile {
  height: 8px;
}

.mask {
  pointer-events: none;

  position: absolute;
  // 偶现兼容性 GPU 渲染问题
  // 
  z-index: 5;
  top: -48px;

  width: 100%;
  height: 48px;

  background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, var(--uikit-chat-input-primary-color, #fff) 100%);
}
