@height: calc(var(--studio-var-textarea-line-height) * 1px);

.editor-render {
  cursor: text;
  resize: none;

  position: relative;

  box-sizing: border-box;
  width: 100%;

  font-size: 14px;
  line-height: 22px;
  vertical-align: bottom;

  background-color: transparent;
  border: 0 solid transparent;
  outline: none;
  box-shadow: none;
}

.container {
  position: relative;
}

.textarea {
  box-sizing: border-box;
  padding: 5px 12px;

  line-height: 22px;
  color: var(--semi-color-text-0);

  background-color: var(--semi-color-white);
  border: 1px var(--semi-color-border) solid;
  border-radius: 8px;

  &:hover {
    background-color: var(--semi-color-fill-0);
  }

  &.focus {
    background-color: var(--semi-color-fill-0);
    border: 1px var(--semi-color-primary) solid;
  }

  &.error {
    border: 1px var(--semi-color-danger) solid;

    &:hover,
    &.focus {
      background-color: var(--semi-color-danger-light-default);
    }
  }
}

.scroller {
  overflow-y: auto;
  height: @height;

  :global {
    div > div > div[data-slate-editor='true'] {
      min-height: @height !important;
    }
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  box-sizing: border-box;
  height: 24px;
  padding: 3px 0 5px;

  font-size: 12px;
  color: var(--semi-color-text-2);
}
