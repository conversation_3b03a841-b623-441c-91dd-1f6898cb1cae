{"name": "@flowgram-adapter/common", "version": "0.0.1", "description": "对 flowgram 的封装", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "types": "src/index.ts", "files": ["src", "bin"], "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@flowgram.ai/command": "0.1.28", "@flowgram.ai/history": "0.1.28", "@flowgram.ai/reactive": "0.1.28", "@flowgram.ai/utils": "0.1.28", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@babel/core": "^7.26.0", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "react-is": ">= 16.8.0", "styled-components": ">=4", "stylelint": "^15.11.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}