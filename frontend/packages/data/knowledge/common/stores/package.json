{"name": "@coze-data/knowledge-stores", "version": "0.0.1", "description": "coze knowledge stores", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "unpkg": "./dist/umd/index.js", "module": "./dist/esm/index.js", "types": "./src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-data/knowledge-resource-processor-core": "workspace:*", "ahooks": "^3.7.8", "classnames": "^2.3.2", "utility-types": "^3.10.0", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "typescript": "~5.8.2", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}