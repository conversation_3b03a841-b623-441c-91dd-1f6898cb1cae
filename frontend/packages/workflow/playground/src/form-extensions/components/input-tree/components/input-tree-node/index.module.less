/* stylelint-disable no-descending-specificity */
.font-normal {
  font-size: 12px;
  font-weight: 500;
  font-style: normal;
  line-height: 20px;
  color: rgba(28, 31, 35, 80%);
}

.param-container {
  position: relative;
  display: flex;
  /* stylelint-disable-next-line declaration-no-important */
  align-items: center !important;
  height: 100%;

  &:first-child {
    &>div:first-child {
      &>div {
        &>div:first-child {
          top: 12px;
        }

        &>div:last-child {
          top: 12px;
        }
      }
    }
  }

  .expand-wrapper {
    display: flex;
    flex-direction: column;

    max-width: 100%;
    margin-top: 4px;
    padding: 4px;

    border-radius: 9px;

    transition: background-color 0.2s ease;
  }

  :global {
    .semi-form-field {
      flex: 1;
    }

    .semi-tree-option-expand-icon {
      display: flex;
      align-items: center;
      height: 24px;
    }

    .semi-input-wrapper-disabled,
    .semi-select-disabled .semi-select-selection-text {
      color: var(--semi-color-text-0);

      -webkit-text-fill-color: var(--semi-color-text-0);
    }

    .semi-cascader-disabled {
      &:active, &:hover {
        background-color: var(--semi-color-disabled-fill);
        border-color: var(--coz-stroke-primary);
      }

      .semi-cascader-arrow, .semi-cascader-selection {
        color: var(--semi-color-text-0);

        -webkit-text-fill-color: var(--semi-color-text-0);
      }
    }
  }

  .wrapper {
    display: flex;
    flex: 1;
    gap: 4px;
    align-items: center;
  }

}
